docxtpl-0.16.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
docxtpl-0.16.7.dist-info/LICENSE.txt,sha256=m4cqigcLitMpxL04D7G_AAD1ZMdQI-yOHmgD8VNkuek,26461
docxtpl-0.16.7.dist-info/METADATA,sha256=KRaHCj145lq2BF12YkxSDR4qgheVP2UkezEbL-eViGY,8357
docxtpl-0.16.7.dist-info/RECORD,,
docxtpl-0.16.7.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docxtpl-0.16.7.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
docxtpl-0.16.7.dist-info/eager_resources.txt,sha256=DasNALQuzzpDEPJb9O4UzE5CjrpnNxe1HOrTNOUH5hs,5
docxtpl-0.16.7.dist-info/top_level.txt,sha256=0Anh3gYd0awULDgYPAMUnFjvYS2qS1WjtX36T1wVWdQ,8
docxtpl/__init__.py,sha256=httSx0gTKP3DGRIcX1VtNbqgz-WJX3ZKMFu9QRdVMU4,280
docxtpl/__main__.py,sha256=aF1bl_bJ8fpxfiJ1J92IG8xlV72yt6d2W9uIsB1gTs8,5517
docxtpl/__pycache__/__init__.cpython-312.pyc,,
docxtpl/__pycache__/__main__.cpython-312.pyc,,
docxtpl/__pycache__/inline_image.cpython-312.pyc,,
docxtpl/__pycache__/listing.cpython-312.pyc,,
docxtpl/__pycache__/richtext.cpython-312.pyc,,
docxtpl/__pycache__/subdoc.cpython-312.pyc,,
docxtpl/__pycache__/template.cpython-312.pyc,,
docxtpl/inline_image.py,sha256=SoFClTsashsuDVRhQdWCqYdIvi0xZ0xJePdFM-Gngss,966
docxtpl/listing.py,sha256=lqxATu9Vdpw1Yu6QCfeiEOGT3-loAY2Li6n0e5X8f80,850
docxtpl/richtext.py,sha256=idTxwmHETOfFcXXeO9iwhF-2Lj1KGU8iJOOCD9Vkk8U,3245
docxtpl/subdoc.py,sha256=TX0ZIF7Jvo1K-V26k5VnJH-RXufSbHe8NvSkF51KZns,3328
docxtpl/template.py,sha256=Ngir07f3-xD9CUwVXcTl1Pah22lXDs2GqzpNoX5NjVo,31801
