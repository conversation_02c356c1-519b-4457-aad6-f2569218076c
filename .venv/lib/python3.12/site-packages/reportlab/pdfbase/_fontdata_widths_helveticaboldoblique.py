widths = {'A': 722,
 'AE': 1000,
 'Aacute': 722,
 'Acircumflex': 722,
 'Adieresis': 722,
 'Agrave': 722,
 'Aring': 722,
 'Atilde': 722,
 'B': 722,
 'C': 722,
 '<PERSON><PERSON><PERSON>': 722,
 'D': 722,
 'E': 667,
 'Eacute': 667,
 'Ecircumflex': 667,
 'Edieresis': 667,
 'Egrave': 667,
 'Eth': 722,
 'Euro': 556,
 'F': 611,
 'G': 778,
 'H': 722,
 'I': 278,
 'Iacute': 278,
 'Icircumflex': 278,
 'Idieresis': 278,
 'Igrave': 278,
 'J': 556,
 'K': 722,
 'L': 611,
 'Lslash': 611,
 'M': 833,
 'N': 722,
 'Ntilde': 722,
 'O': 778,
 'OE': 1000,
 'Oacute': 778,
 'Ocircumflex': 778,
 'Odieresis': 778,
 'Ograve': 778,
 'Oslash': 778,
 'Otilde': 778,
 'P': 667,
 'Q': 778,
 'R': 722,
 '<PERSON>': 667,
 'Scaron': 667,
 'T': 611,
 'Thorn': 667,
 'U': 722,
 'Uacute': 722,
 'Ucircumflex': 722,
 'Udieresis': 722,
 'Ugrave': 722,
 'V': 667,
 'W': 944,
 'X': 667,
 'Y': 667,
 'Yacute': 667,
 'Ydieresis': 667,
 'Z': 611,
 'Zcaron': 611,
 'a': 556,
 'aacute': 556,
 'acircumflex': 556,
 'acute': 333,
 'adieresis': 556,
 'ae': 889,
 'agrave': 556,
 'ampersand': 722,
 'aring': 556,
 'asciicircum': 584,
 'asciitilde': 584,
 'asterisk': 389,
 'at': 975,
 'atilde': 556,
 'b': 611,
 'backslash': 278,
 'bar': 280,
 'braceleft': 389,
 'braceright': 389,
 'bracketleft': 333,
 'bracketright': 333,
 'breve': 333,
 'brokenbar': 280,
 'bullet': 350,
 'c': 556,
 'caron': 333,
 'ccedilla': 556,
 'cedilla': 333,
 'cent': 556,
 'circumflex': 333,
 'colon': 333,
 'comma': 278,
 'copyright': 737,
 'currency': 556,
 'd': 611,
 'dagger': 556,
 'daggerdbl': 556,
 'degree': 400,
 'dieresis': 333,
 'divide': 584,
 'dollar': 556,
 'dotaccent': 333,
 'dotlessi': 278,
 'e': 556,
 'eacute': 556,
 'ecircumflex': 556,
 'edieresis': 556,
 'egrave': 556,
 'eight': 556,
 'ellipsis': 1000,
 'emdash': 1000,
 'endash': 556,
 'equal': 584,
 'eth': 611,
 'exclam': 333,
 'exclamdown': 333,
 'f': 333,
 'fi': 611,
 'five': 556,
 'fl': 611,
 'florin': 556,
 'four': 556,
 'fraction': 167,
 'g': 611,
 'germandbls': 611,
 'grave': 333,
 'greater': 584,
 'guillemotleft': 556,
 'guillemotright': 556,
 'guilsinglleft': 333,
 'guilsinglright': 333,
 'h': 611,
 'hungarumlaut': 333,
 'hyphen': 333,
 'i': 278,
 'iacute': 278,
 'icircumflex': 278,
 'idieresis': 278,
 'igrave': 278,
 'j': 278,
 'k': 556,
 'l': 278,
 'less': 584,
 'logicalnot': 584,
 'lslash': 278,
 'm': 889,
 'macron': 333,
 'minus': 584,
 'mu': 611,
 'multiply': 584,
 'n': 611,
 'nine': 556,
 'ntilde': 611,
 'numbersign': 556,
 'o': 611,
 'oacute': 611,
 'ocircumflex': 611,
 'odieresis': 611,
 'oe': 944,
 'ogonek': 333,
 'ograve': 611,
 'one': 556,
 'onehalf': 834,
 'onequarter': 834,
 'onesuperior': 333,
 'ordfeminine': 370,
 'ordmasculine': 365,
 'oslash': 611,
 'otilde': 611,
 'p': 611,
 'paragraph': 556,
 'parenleft': 333,
 'parenright': 333,
 'percent': 889,
 'period': 278,
 'periodcentered': 278,
 'perthousand': 1000,
 'plus': 584,
 'plusminus': 584,
 'q': 611,
 'question': 611,
 'questiondown': 611,
 'quotedbl': 474,
 'quotedblbase': 500,
 'quotedblleft': 500,
 'quotedblright': 500,
 'quoteleft': 278,
 'quoteright': 278,
 'quotesinglbase': 278,
 'quotesingle': 238,
 'r': 389,
 'registered': 737,
 'ring': 333,
 's': 556,
 'scaron': 556,
 'section': 556,
 'semicolon': 333,
 'seven': 556,
 'six': 556,
 'slash': 278,
 'space': 278,
 'sterling': 556,
 't': 333,
 'thorn': 611,
 'three': 556,
 'threequarters': 834,
 'threesuperior': 333,
 'tilde': 333,
 'trademark': 1000,
 'two': 556,
 'twosuperior': 333,
 'u': 611,
 'uacute': 611,
 'ucircumflex': 611,
 'udieresis': 611,
 'ugrave': 611,
 'underscore': 556,
 'v': 556,
 'w': 778,
 'x': 556,
 'y': 556,
 'yacute': 556,
 'ydieresis': 556,
 'yen': 556,
 'z': 500,
 'zcaron': 500,
 'zero': 556}
