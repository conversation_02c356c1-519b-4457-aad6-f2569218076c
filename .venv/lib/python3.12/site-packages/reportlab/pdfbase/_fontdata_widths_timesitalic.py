widths = {'A': 611,
 'AE': 889,
 'Aacute': 611,
 'Acircumflex': 611,
 'Adieresis': 611,
 'Agrave': 611,
 'Aring': 611,
 'Atilde': 611,
 'B': 611,
 'C': 667,
 '<PERSON><PERSON><PERSON>': 667,
 'D': 722,
 'E': 611,
 'Eacute': 611,
 'Ecircumflex': 611,
 'Edieresis': 611,
 'Egrave': 611,
 'Eth': 722,
 'Euro': 500,
 'F': 611,
 'G': 722,
 'H': 722,
 'I': 333,
 'Iacute': 333,
 'Icircumflex': 333,
 'Idieresis': 333,
 'Igrave': 333,
 'J': 444,
 'K': 667,
 'L': 556,
 'Lslash': 556,
 'M': 833,
 'N': 667,
 'Ntilde': 667,
 'O': 722,
 'OE': 944,
 'Oacute': 722,
 'Ocircumflex': 722,
 'Odieresis': 722,
 'Ograve': 722,
 'Oslash': 722,
 'Otilde': 722,
 'P': 611,
 'Q': 722,
 'R': 611,
 'S': 500,
 'Scaron': 500,
 'T': 556,
 'Thorn': 611,
 'U': 722,
 'Uacute': 722,
 'Ucircumflex': 722,
 'Udieresis': 722,
 'Ugrave': 722,
 'V': 611,
 'W': 833,
 'X': 611,
 'Y': 556,
 'Yacute': 556,
 'Ydieresis': 556,
 'Z': 556,
 'Zcaron': 556,
 'a': 500,
 'aacute': 500,
 'acircumflex': 500,
 'acute': 333,
 'adieresis': 500,
 'ae': 667,
 'agrave': 500,
 'ampersand': 778,
 'aring': 500,
 'asciicircum': 422,
 'asciitilde': 541,
 'asterisk': 500,
 'at': 920,
 'atilde': 500,
 'b': 500,
 'backslash': 278,
 'bar': 275,
 'braceleft': 400,
 'braceright': 400,
 'bracketleft': 389,
 'bracketright': 389,
 'breve': 333,
 'brokenbar': 275,
 'bullet': 350,
 'c': 444,
 'caron': 333,
 'ccedilla': 444,
 'cedilla': 333,
 'cent': 500,
 'circumflex': 333,
 'colon': 333,
 'comma': 250,
 'copyright': 760,
 'currency': 500,
 'd': 500,
 'dagger': 500,
 'daggerdbl': 500,
 'degree': 400,
 'dieresis': 333,
 'divide': 675,
 'dollar': 500,
 'dotaccent': 333,
 'dotlessi': 278,
 'e': 444,
 'eacute': 444,
 'ecircumflex': 444,
 'edieresis': 444,
 'egrave': 444,
 'eight': 500,
 'ellipsis': 889,
 'emdash': 889,
 'endash': 500,
 'equal': 675,
 'eth': 500,
 'exclam': 333,
 'exclamdown': 389,
 'f': 278,
 'fi': 500,
 'five': 500,
 'fl': 500,
 'florin': 500,
 'four': 500,
 'fraction': 167,
 'g': 500,
 'germandbls': 500,
 'grave': 333,
 'greater': 675,
 'guillemotleft': 500,
 'guillemotright': 500,
 'guilsinglleft': 333,
 'guilsinglright': 333,
 'h': 500,
 'hungarumlaut': 333,
 'hyphen': 333,
 'i': 278,
 'iacute': 278,
 'icircumflex': 278,
 'idieresis': 278,
 'igrave': 278,
 'j': 278,
 'k': 444,
 'l': 278,
 'less': 675,
 'logicalnot': 675,
 'lslash': 278,
 'm': 722,
 'macron': 333,
 'minus': 675,
 'mu': 500,
 'multiply': 675,
 'n': 500,
 'nine': 500,
 'ntilde': 500,
 'numbersign': 500,
 'o': 500,
 'oacute': 500,
 'ocircumflex': 500,
 'odieresis': 500,
 'oe': 667,
 'ogonek': 333,
 'ograve': 500,
 'one': 500,
 'onehalf': 750,
 'onequarter': 750,
 'onesuperior': 300,
 'ordfeminine': 276,
 'ordmasculine': 310,
 'oslash': 500,
 'otilde': 500,
 'p': 500,
 'paragraph': 523,
 'parenleft': 333,
 'parenright': 333,
 'percent': 833,
 'period': 250,
 'periodcentered': 250,
 'perthousand': 1000,
 'plus': 675,
 'plusminus': 675,
 'q': 500,
 'question': 500,
 'questiondown': 500,
 'quotedbl': 420,
 'quotedblbase': 556,
 'quotedblleft': 556,
 'quotedblright': 556,
 'quoteleft': 333,
 'quoteright': 333,
 'quotesinglbase': 333,
 'quotesingle': 214,
 'r': 389,
 'registered': 760,
 'ring': 333,
 's': 389,
 'scaron': 389,
 'section': 500,
 'semicolon': 333,
 'seven': 500,
 'six': 500,
 'slash': 278,
 'space': 250,
 'sterling': 500,
 't': 278,
 'thorn': 500,
 'three': 500,
 'threequarters': 750,
 'threesuperior': 300,
 'tilde': 333,
 'trademark': 980,
 'two': 500,
 'twosuperior': 300,
 'u': 500,
 'uacute': 500,
 'ucircumflex': 500,
 'udieresis': 500,
 'ugrave': 500,
 'underscore': 500,
 'v': 444,
 'w': 667,
 'x': 444,
 'y': 444,
 'yacute': 444,
 'ydieresis': 444,
 'yen': 500,
 'z': 389,
 'zcaron': 389,
 'zero': 500}
